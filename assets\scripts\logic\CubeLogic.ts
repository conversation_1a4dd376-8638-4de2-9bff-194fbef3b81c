/**
 * 核心游戏逻辑
 * 移植自原Logic部分
 */
import { ICube } from "../models/ICube";
import { GameState } from "../models/ICube";
import EnumHelper from "../helpers/EnumHelper";
import { CubeGenerator } from "../utils/CubeGenerator";

export class CubeLogic {
    private _gameState: GameState = GameState.Playing;
    private _cubeArray: ICube[] = [];
    private _score: number = 0;

    constructor(initialArray: ICube[] = []) {
        this._cubeArray = [...initialArray];
    }

    /**
     * 检查并执行方块消除
     */
    checkRemove(index: number): boolean {
        const target = this._cubeArray[index];
        if (!target || target.color === null) return false;

        const sameColorIndices = this.findSameColor(index, target.color);
        if (sameColorIndices.length < EnumHelper.CommonEnum.minRemoveCount) {
            return false;
        }

        // 执行消除
        sameColorIndices.forEach(i => {
            this._cubeArray[i].color = null;
        });

        this._score += sameColorIndices.length * EnumHelper.CommonEnum.scorePerCube;
        return true;
    }

    /**
     * 查找相同颜色方块的递归实现
     */
    private findSameColor(index: number, color: number, checked: number[] = []): number[] {
        if (checked.indexOf(index) !== -1) return [];
        checked.push(index);

        const neighbors = this.getNeighborIndices(index);
        let result = [index];

        neighbors.forEach(neighborIndex => {
            const neighbor = this._cubeArray[neighborIndex];
            if (neighbor && neighbor.color === color) {
                result = [...result, ...this.findSameColor(neighborIndex, color, checked)];
            }
        });

        return result;
    }

    /**
     * 整理方块（下落和补充新方块）
     */
    arrangeCube(): void {
        // 下落逻辑
        for (let col = 0; col < EnumHelper.ChessBoard.ChessWidth; col++) {
            let emptyIndex = -1;

            for (let row = EnumHelper.ChessBoard.ChessHeight - 1; row >= 0; row--) {
                const index = row * EnumHelper.ChessBoard.ChessWidth + col;
                if (this._cubeArray[index].color === null) {
                    if (emptyIndex === -1) emptyIndex = index;
                } else if (emptyIndex !== -1) {
                    // 方块下落
                    this._cubeArray[emptyIndex] = {...this._cubeArray[index]};
                    this._cubeArray[index].color = null;
                    emptyIndex--;
                }
            }
        }

        // 补充新方块
        const emptyCount = this._cubeArray.filter(c => c.color === null).length;
        if (emptyCount > 0) {
            const newCubes = CubeGenerator.generateMultiple(
                EnumHelper.CommonEnum.maxCubeArrayLength,
                false,
                emptyCount
            );

            let newCubeIndex = 0;
            this._cubeArray.forEach((cube, index) => {
                if (cube.color === null && newCubeIndex < newCubes.length) {
                    this._cubeArray[index] = newCubes[newCubeIndex++];
                }
            });
        }
    }

    /**
     * 获取相邻方块索引
     */
    private getNeighborIndices(index: number): number[] {
        const row = Math.floor(index / EnumHelper.ChessBoard.ChessWidth);
        const col = index % EnumHelper.ChessBoard.ChessWidth;
        const neighbors = [];

        // 上
        if (row > 0) neighbors.push(index - EnumHelper.ChessBoard.ChessWidth);
        // 下
        if (row < EnumHelper.ChessBoard.ChessHeight - 1) neighbors.push(index + EnumHelper.ChessBoard.ChessWidth);
        // 左
        if (col > 0) neighbors.push(index - 1);
        // 右
        if (col < EnumHelper.ChessBoard.ChessWidth - 1) neighbors.push(index + 1);

        return neighbors;
    }

    // 其他游戏逻辑方法...
    get score(): number { return this._score; }
    get gameState(): GameState { return this._gameState; }
    get cubeArray(): ICube[] { return [...this._cubeArray]; }
}