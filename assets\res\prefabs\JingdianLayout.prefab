[{"__type__": "cc.Prefab", "_name": "JingdianLayout", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "JingdianLayout", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}], "_prefab": {"__id__": 56}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "jiangdian", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 45}, {"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "mainPanel", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 14}, {"__id__": 22}, {"__id__": 30}], "_active": true, "_components": [{"__id__": 38}, {"__id__": 40}, {"__id__": 42}], "_prefab": {"__id__": 44}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8885, "y": 0.7498, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}, {"__id__": 9}, {"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ba5c6211-2398-4134-aa1b-e221409ff2ae@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1A0PVHPlPWJrIxmaYQ1Yb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "000KLBdrVOiIphiLT9Wj8W"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 10}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cYYwiQzVMFK6uxjt1ZR1y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 12}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "33LlBFlQhFBKb247j5lQaw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "83bCOXBaVPBry70O+pdNcm"}, {"__type__": "cc.Node", "_name": "ImageView_406", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}, {"__id__": 19}], "_prefab": {"__id__": 21}, "_lpos": {"__type__": "cc.Vec3", "x": 136, "y": 1211, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9877, "y": 1.1793, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 16}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "38418c7f-afa2-4647-a6a0-5bd4165daf6c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59tz5nWbFN+JIYaJ86Bf27"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 18}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cO3/am4xOKoRO9Sz+AzrA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 20}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 236, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "67RB3brdpF7J98Fc3DWB+I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53v3rRi/BCO7tR+M44aQp/"}, {"__type__": "cc.Node", "_name": "LabelAtlas_407", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 23}, {"__id__": 25}, {"__id__": 27}], "_prefab": {"__id__": 29}, "_lpos": {"__type__": "cc.Vec3", "x": 64, "y": 1211, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_type": 2, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": {"__uuid__": "7abe041c-e936-41c5-9c43-8968a54cc0da"}, "firstChar": ".", "charWidth": 37, "charHeight": 34, "string": "0", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": true, "listDirection": 0, "listHorizontalAlign": 0, "listVerticalAlign": 0, "listPadding": 0, "_id": "", "__prefab": {"__id__": 24}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b70Bcmu+tGULAnLWENVd/i"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 26}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "4ewFiKk4dIfpNEmj2pFmtJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 28}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 37, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd2fw/L4dPzIarO7bgZS+s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13lLnK8oFDfbYBrKOf97nW"}, {"__type__": "cc.Node", "_name": "Label_603", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 173.9998, "y": 1208.9998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 32}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "01234567890", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "2768b951-ce1e-4ed2-acac-6ae1dfe4129e"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9ITMB3dxAo7uSxTK7kceJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 34}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8b24U/J99GNJMIJOGGCWWi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 36}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7enSD0aFBMA7Cz6SctMvkL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85MAgWksFEkI+98821GaLn"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 39}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84oEG3HQJA/4VsauLR5uGS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 41}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe7XEG5IVIgZq5veUSLyvi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 43}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.CompPrefabInfo", "fileId": "06umyPOPVDoL6LVfkJaOC5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78SWp7WrNFCJ4hvUOJ/+YR"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 46}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbvfA97nhEgpxlcDQLYHII"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 48}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "daT72awetCCYbuGzqLexe6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 50}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f90BrZ4g5GoJn/th5mD9P6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7daR8KTMtJC58TGCNdnmDY"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 53}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "73EdOsjjlAXJ/Vtok985zW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 55}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cj8YyYuxB5qeuOCO5DtvY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6J3y9JQBHnIRqkdS/EV/A"}]